{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"Panele Cekme Bot v1/1.0.0": {"dependencies": {"HtmlAgilityPack": "1.12.2", "Microsoft.Extensions.Configuration.Json": "9.0.8", "Microsoft.Extensions.Hosting": "9.0.8", "Microsoft.Extensions.Logging.Console": "9.0.8", "Newtonsoft.Json": "13.0.3", "Serilog": "4.1.0", "Serilog.Extensions.Hosting": "8.0.0", "Serilog.Sinks.Console": "6.0.0", "Serilog.Sinks.File": "6.0.0"}, "runtime": {"Panele Cekme Bot v1.dll": {}}}, "HtmlAgilityPack/1.12.2": {"runtime": {"lib/net7.0/HtmlAgilityPack.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Extensions.Configuration/9.0.8": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.8", "Microsoft.Extensions.Primitives": "9.0.8"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.8": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.8"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Configuration.Binder/9.0.8": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.8"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Configuration.CommandLine/9.0.8": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.8", "Microsoft.Extensions.Configuration.Abstractions": "9.0.8"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.CommandLine.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/9.0.8": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.8", "Microsoft.Extensions.Configuration.Abstractions": "9.0.8"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.8": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.8", "Microsoft.Extensions.Configuration.Abstractions": "9.0.8", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.8", "Microsoft.Extensions.FileProviders.Physical": "9.0.8", "Microsoft.Extensions.Primitives": "9.0.8"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Configuration.Json/9.0.8": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.8", "Microsoft.Extensions.Configuration.Abstractions": "9.0.8", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.8", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.8", "System.Text.Json": "9.0.8"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Configuration.UserSecrets/9.0.8": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.8", "Microsoft.Extensions.Configuration.Json": "9.0.8", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.8", "Microsoft.Extensions.FileProviders.Physical": "9.0.8"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.DependencyInjection/9.0.8": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.8": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Diagnostics/9.0.8": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.8", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.8", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.8"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Diagnostics.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.8": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8", "Microsoft.Extensions.Options": "9.0.8", "System.Diagnostics.DiagnosticSource": "9.0.8"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.8": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.8"}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.FileProviders.Physical/9.0.8": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.8", "Microsoft.Extensions.FileSystemGlobbing": "9.0.8", "Microsoft.Extensions.Primitives": "9.0.8"}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.FileSystemGlobbing/9.0.8": {"runtime": {"lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Hosting/9.0.8": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.8", "Microsoft.Extensions.Configuration.Abstractions": "9.0.8", "Microsoft.Extensions.Configuration.Binder": "9.0.8", "Microsoft.Extensions.Configuration.CommandLine": "9.0.8", "Microsoft.Extensions.Configuration.EnvironmentVariables": "9.0.8", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.8", "Microsoft.Extensions.Configuration.Json": "9.0.8", "Microsoft.Extensions.Configuration.UserSecrets": "9.0.8", "Microsoft.Extensions.DependencyInjection": "9.0.8", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8", "Microsoft.Extensions.Diagnostics": "9.0.8", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.8", "Microsoft.Extensions.FileProviders.Physical": "9.0.8", "Microsoft.Extensions.Hosting.Abstractions": "9.0.8", "Microsoft.Extensions.Logging": "9.0.8", "Microsoft.Extensions.Logging.Abstractions": "9.0.8", "Microsoft.Extensions.Logging.Configuration": "9.0.8", "Microsoft.Extensions.Logging.Console": "9.0.8", "Microsoft.Extensions.Logging.Debug": "9.0.8", "Microsoft.Extensions.Logging.EventLog": "9.0.8", "Microsoft.Extensions.Logging.EventSource": "9.0.8", "Microsoft.Extensions.Options": "9.0.8"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Hosting.Abstractions/9.0.8": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.8", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.8", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.8", "Microsoft.Extensions.Logging.Abstractions": "9.0.8"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Logging/9.0.8": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.8", "Microsoft.Extensions.Logging.Abstractions": "9.0.8", "Microsoft.Extensions.Options": "9.0.8"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.8": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8", "System.Diagnostics.DiagnosticSource": "9.0.8"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Logging.Configuration/9.0.8": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.8", "Microsoft.Extensions.Configuration.Abstractions": "9.0.8", "Microsoft.Extensions.Configuration.Binder": "9.0.8", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8", "Microsoft.Extensions.Logging": "9.0.8", "Microsoft.Extensions.Logging.Abstractions": "9.0.8", "Microsoft.Extensions.Options": "9.0.8", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.8"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Logging.Console/9.0.8": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8", "Microsoft.Extensions.Logging": "9.0.8", "Microsoft.Extensions.Logging.Abstractions": "9.0.8", "Microsoft.Extensions.Logging.Configuration": "9.0.8", "Microsoft.Extensions.Options": "9.0.8", "System.Text.Json": "9.0.8"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Console.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Logging.Debug/9.0.8": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8", "Microsoft.Extensions.Logging": "9.0.8", "Microsoft.Extensions.Logging.Abstractions": "9.0.8"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Logging.EventLog/9.0.8": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8", "Microsoft.Extensions.Logging": "9.0.8", "Microsoft.Extensions.Logging.Abstractions": "9.0.8", "Microsoft.Extensions.Options": "9.0.8", "System.Diagnostics.EventLog": "9.0.8"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Logging.EventSource/9.0.8": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8", "Microsoft.Extensions.Logging": "9.0.8", "Microsoft.Extensions.Logging.Abstractions": "9.0.8", "Microsoft.Extensions.Options": "9.0.8", "Microsoft.Extensions.Primitives": "9.0.8", "System.Text.Json": "9.0.8"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.EventSource.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Options/9.0.8": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8", "Microsoft.Extensions.Primitives": "9.0.8"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.8": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.8", "Microsoft.Extensions.Configuration.Binder": "9.0.8", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8", "Microsoft.Extensions.Options": "9.0.8", "Microsoft.Extensions.Primitives": "9.0.8"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Microsoft.Extensions.Primitives/9.0.8": {"runtime": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "********", "fileVersion": "13.0.3.27908"}}}, "Serilog/4.1.0": {"runtime": {"lib/net8.0/Serilog.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Extensions.Hosting/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.8", "Microsoft.Extensions.Hosting.Abstractions": "9.0.8", "Microsoft.Extensions.Logging.Abstractions": "9.0.8", "Serilog": "4.1.0", "Serilog.Extensions.Logging": "8.0.0"}, "runtime": {"lib/net8.0/Serilog.Extensions.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Extensions.Logging/8.0.0": {"dependencies": {"Microsoft.Extensions.Logging": "9.0.8", "Serilog": "4.1.0"}, "runtime": {"lib/net8.0/Serilog.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.Console/6.0.0": {"dependencies": {"Serilog": "4.1.0"}, "runtime": {"lib/net8.0/Serilog.Sinks.Console.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.File/6.0.0": {"dependencies": {"Serilog": "4.1.0"}, "runtime": {"lib/net8.0/Serilog.Sinks.File.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "System.Diagnostics.DiagnosticSource/9.0.8": {"runtime": {"lib/net8.0/System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "System.Diagnostics.EventLog/9.0.8": {"runtime": {"lib/net8.0/System.Diagnostics.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Diagnostics.EventLog.Messages.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "0.0.0.0"}, "runtimes/win/lib/net8.0/System.Diagnostics.EventLog.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "System.IO.Pipelines/9.0.8": {"runtime": {"lib/net8.0/System.IO.Pipelines.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "System.Text.Encodings.Web/9.0.8": {"runtime": {"lib/net8.0/System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}, "runtimeTargets": {"runtimes/browser/lib/net8.0/System.Text.Encodings.Web.dll": {"rid": "browser", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}, "System.Text.Json/9.0.8": {"dependencies": {"System.IO.Pipelines": "9.0.8", "System.Text.Encodings.Web": "9.0.8"}, "runtime": {"lib/net8.0/System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.825.36511"}}}}}, "libraries": {"Panele Cekme Bot v1/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "HtmlAgilityPack/1.12.2": {"type": "package", "serviceable": true, "sha512": "sha512-btF/9sB65h0V9ipZxVfEQ9fxDwXSFRwhi4Z1qFBgnXONqWVKZE3LxS0JEMW73G3gvrFI7/IAqLA1y/15HDa3fw==", "path": "htmlagilitypack/1.12.2", "hashPath": "htmlagilitypack.1.12.2.nupkg.sha512"}, "Microsoft.Extensions.Configuration/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-6m+8Xgmf8UWL0p/oGqBM+0KbHE5/ePXbV1hKXgC59zEv0aa0DW5oiiyxDbK5kH5j4gIvyD5uWL0+HadKBJngvQ==", "path": "microsoft.extensions.configuration/9.0.8", "hashPath": "microsoft.extensions.configuration.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-yNou2KM35RvzOh4vUFtl2l33rWPvOCoba+nzEDJ+BgD8aOL/jew4WPCibQvntRfOJ2pJU8ARygSMD+pdjvDHuA==", "path": "microsoft.extensions.configuration.abstractions/9.0.8", "hashPath": "microsoft.extensions.configuration.abstractions.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-0vK9DnYrYChdiH3yRZWkkp4x4LbrfkWEdBc5HOsQ8t/0CLOWKXKkkhOE8A1shlex0hGydbGrhObeypxz/QTm+w==", "path": "microsoft.extensions.configuration.binder/9.0.8", "hashPath": "microsoft.extensions.configuration.binder.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Configuration.CommandLine/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-vB6eDQ5prED5jHBqmSDNYzlCXsTSylYY7co9c7guhnz0zhx+jZ8BTHgO7y/Wl1dV2jAO15mKNWuyHRIRtWwGQg==", "path": "microsoft.extensions.configuration.commandline/9.0.8", "hashPath": "microsoft.extensions.configuration.commandline.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Configuration.EnvironmentVariables/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-9qileEYXDodlPN9DfPd5sHSfU2nSrI1r5BHVqLaLyb/7mPi335cy4ar/0ix4tXb2Aer/Pu4e5/zdwxt7lrtSyQ==", "path": "microsoft.extensions.configuration.environmentvariables/9.0.8", "hashPath": "microsoft.extensions.configuration.environmentvariables.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-2jgx58Jpk3oKT7KRn8x/cFf3QDTjQP+KUbyBnynAcB2iBx1Eq9EdNMCu0QEbYuaZOaQru/Kwdffary+hn58Wwg==", "path": "microsoft.extensions.configuration.fileextensions/9.0.8", "hashPath": "microsoft.extensions.configuration.fileextensions.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-vjxzcnL7ul322+kpvELisXaZl8/5MYs6JfI9DZLQWsao1nA/4FL48yPwDK986hbJTWc64JxOOaMym0SQ/dy32w==", "path": "microsoft.extensions.configuration.json/9.0.8", "hashPath": "microsoft.extensions.configuration.json.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Configuration.UserSecrets/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-UgH18nQkuMJgxjn1539I83N6LhnKQlLhQm3ppe+PGsFpYsC6eGpF/1KvDRm/bmqsrg0NXhurrv4k2r0e8vWX/Q==", "path": "microsoft.extensions.configuration.usersecrets/9.0.8", "hashPath": "microsoft.extensions.configuration.usersecrets.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-JJjI2Fa+QtZcUyuNjbKn04OjIUX5IgFGFu/Xc+qvzh1rXdZHLcnqqVXhR4093bGirTwacRlHiVg1XYI9xum6QQ==", "path": "microsoft.extensions.dependencyinjection/9.0.8", "hashPath": "microsoft.extensions.dependencyinjection.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-xY3lTjj4+ZYmiKIkyWitddrp1uL5uYiweQjqo4BKBw01ZC4HhcfgLghDpPZcUlppgWAFqFy9SgkiYWOMx365pw==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.8", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-BKkLCFXzJvNmdngeYBf72VXoZqTJSb1orvjdzDLaGobicoGFBPW8ug2ru1nnEewMEwJzMgnsjHQY8EaKWmVhKg==", "path": "microsoft.extensions.diagnostics/9.0.8", "hashPath": "microsoft.extensions.diagnostics.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-UDY7blv4DCyIJ/8CkNrQKLaAZFypXQavRZ2DWf/2zi1mxYYKKw2t8AOCBWxNntyPZHPGhtEmL3snFM98ADZqTw==", "path": "microsoft.extensions.diagnostics.abstractions/9.0.8", "hashPath": "microsoft.extensions.diagnostics.abstractions.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-4zZbQ4w+hCMm9J+z5NOj3giIPT2MhZxx05HX/MGuAmDBbjOuXlYIIRN+t4V6OLxy5nXZIcXO+dQMB/OWubuDkw==", "path": "microsoft.extensions.fileproviders.abstractions/9.0.8", "hashPath": "microsoft.extensions.fileproviders.abstractions.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-FlOe2i7UUIfY0l0ChaIYtlXjdWWutR4DMRKZaGD6z4G1uVTteFkbBfxUIoi1uGmrZQxXe/yv/cfwiT0tK2xyXA==", "path": "microsoft.extensions.fileproviders.physical/9.0.8", "hashPath": "microsoft.extensions.fileproviders.physical.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-96Ub5LmwYfIGVoXkbe4kjs+ivK6fLBTwKJAOMfUNV0R+AkZRItlgROFqXEWMUlXBTPM1/kKu26Ueu5As6RDzJA==", "path": "microsoft.extensions.filesystemglobbing/9.0.8", "hashPath": "microsoft.extensions.filesystemglobbing.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Hosting/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-O2VlzORrBbS2it203k5FOHrudDdmdrJovA73P/shdRGeLzvet4e4yXhGx52V2PNjYBQ0IO5M4xiNcL+6xIX6Bg==", "path": "microsoft.extensions.hosting/9.0.8", "hashPath": "microsoft.extensions.hosting.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-W<PERSON>rad20tySNCPe9aJUK7Wfwh+RiyLF+id02FKW8Qfc+HAzNQHazcqMXAbwG/kmbS89uvan/nKK1MufkRahjrJA==", "path": "microsoft.extensions.hosting.abstractions/9.0.8", "hashPath": "microsoft.extensions.hosting.abstractions.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Logging/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-Z/7ze+0iheT7FJeZPqJKARYvyC2bmwu3whbm/48BJjdlGVvgDguoCqJIkI/67NkroTYobd5geai1WheNQvWrgA==", "path": "microsoft.extensions.logging/9.0.8", "hashPath": "microsoft.extensions.logging.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-pYnAffJL7ARD/HCnnPvnFKSIHnTSmWz84WIlT9tPeQ4lHNiu0Az7N/8itihWvcF8sT+VVD5lq8V+ckMzu4SbOw==", "path": "microsoft.extensions.logging.abstractions/9.0.8", "hashPath": "microsoft.extensions.logging.abstractions.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Logging.Configuration/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-Us4evDN3lbp1beVgrpxkSXKrbntVGAK+YbSo9P9driiU9PK05+ShhgesJ3aj7SuDfr3mqqcEgrMJ87Vu8t5dhw==", "path": "microsoft.extensions.logging.configuration/9.0.8", "hashPath": "microsoft.extensions.logging.configuration.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Logging.Console/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-mPp9xB9MjiPuodh9z/+6zEGNj2kSVeXQtdbIBHlhUYqxX22gzJkx0ycPY42q4/OT/SzFV/TJ989Pa3sA/8ZBeA==", "path": "microsoft.extensions.logging.console/9.0.8", "hashPath": "microsoft.extensions.logging.console.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Logging.Debug/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-OwHQFVITsONEoizShc1yNYTUvMq0kT9j/LhwAKMsA7OZqtrBXuqjosbSvzkJZ9o+KWAozDh5Y1Vtpe5p/8/1qA==", "path": "microsoft.extensions.logging.debug/9.0.8", "hashPath": "microsoft.extensions.logging.debug.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventLog/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-/gMwlll21UJcaXlitUqd+rs9jH36EJz5BpFVPshyOqz5u0qyV1pFnTWm5vhyx+g6gwVYENSLgpazR1urNv83xw==", "path": "microsoft.extensions.logging.eventlog/9.0.8", "hashPath": "microsoft.extensions.logging.eventlog.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventSource/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-aGMFc/1P+315d07iyxSe6lEoZ0JjOPJ+Mfv9rrV2PvR2DFu1/pSi/SItHw1iChJOZgslNKJE97g1a9nLX3qQYA==", "path": "microsoft.extensions.logging.eventsource/9.0.8", "hashPath": "microsoft.extensions.logging.eventsource.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-OmTaQ0v4gxGQkehpwWIqPoEiwsPuG/u4HUsbOFoWGx4DKET2AXzopnFe/fE608FIhzc/kcg2p8JdyMRCCUzitQ==", "path": "microsoft.extensions.options/9.0.8", "hashPath": "microsoft.extensions.options.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-eW2s6n06x0w6w4nsX+SvpgsFYkl+Y0CttYAt6DKUXeqprX+hzNqjSfOh637fwNJBg7wRBrOIRHe49gKiTgJxzQ==", "path": "microsoft.extensions.options.configurationextensions/9.0.8", "hashPath": "microsoft.extensions.options.configurationextensions.9.0.8.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-tizSIOEsIgSNSSh+hKeUVPK7xmTIjR8s+mJWOu1KXV3htvNQiPMFRMO17OdI1y/4ZApdBVk49u/08QGC9yvLug==", "path": "microsoft.extensions.primitives/9.0.8", "hashPath": "microsoft.extensions.primitives.9.0.8.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "Serilog/4.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-u1aZI8HZ62LWlq5dZLFwm6jMax/sUwnWZSw5lkPsCt518cJBxFKoNmc7oSxe5aA5BgSkzy9rzwFGR/i/acnSPw==", "path": "serilog/4.1.0", "hashPath": "serilog.4.1.0.nupkg.sha512"}, "Serilog.Extensions.Hosting/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-db0OcbWeSCvYQkHWu6n0v40N4kKaTAXNjlM3BKvcbwvNzYphQFcBR+36eQ/7hMMwOkJvAyLC2a9/jNdUL5NjtQ==", "path": "serilog.extensions.hosting/8.0.0", "hashPath": "serilog.extensions.hosting.8.0.0.nupkg.sha512"}, "Serilog.Extensions.Logging/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-YEAMWu1UnWgf1c1KP85l1SgXGfiVo0Rz6x08pCiPOIBt2Qe18tcZLvdBUuV5o1QHvrs8FAry9wTIhgBRtjIlEg==", "path": "serilog.extensions.logging/8.0.0", "hashPath": "serilog.extensions.logging.8.0.0.nupkg.sha512"}, "Serilog.Sinks.Console/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fQGWqVMClCP2yEyTXPIinSr5c+CBGUvBybPxjAGcf7ctDhadFhrQw03Mv8rJ07/wR5PDfFjewf2LimvXCDzpbA==", "path": "serilog.sinks.console/6.0.0", "hashPath": "serilog.sinks.console.6.0.0.nupkg.sha512"}, "Serilog.Sinks.File/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-lxjg89Y8gJMmFxVkbZ+qDgjl+T4yC5F7WSLTvA+5q0R04tfKVLRL/EHpYoJ/MEQd2EeCKDuylBIVnAYMotmh2A==", "path": "serilog.sinks.file/6.0.0", "hashPath": "serilog.sinks.file.6.0.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-Lj8/a1Hzli1z6jo8H9urc16GxkpVJtJM+W9fmivXMNu7nwzHziGkxn4vO0DFscMbudkEVKSezdDuHk5kgM0X/g==", "path": "system.diagnostics.diagnosticsource/9.0.8", "hashPath": "system.diagnostics.diagnosticsource.9.0.8.nupkg.sha512"}, "System.Diagnostics.EventLog/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-gebRF3JLLJ76jz1CQpvwezNapZUjFq20JQsaGHzBH0DzlkHBLpdhwkOei9usiOkIGMwU/L0ALWpNe1JE+5/itw==", "path": "system.diagnostics.eventlog/9.0.8", "hashPath": "system.diagnostics.eventlog.9.0.8.nupkg.sha512"}, "System.IO.Pipelines/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-6vPmJt73mgUo1gzc/OcXlJvClz/2jxZ4TQPRfriVaLoGRH2mye530D9WHJYbFQRNMxF3PWCoeofsFdCyN7fLzA==", "path": "system.io.pipelines/9.0.8", "hashPath": "system.io.pipelines.9.0.8.nupkg.sha512"}, "System.Text.Encodings.Web/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-W+LotQsM4wBJ4PG7uRkyul4wqL4Fz7R4ty6uXrCNZUhbaHYANgrPaYR2ZpMVpdCjQEJ17Jr1NMN8hv4SHaHY4A==", "path": "system.text.encodings.web/9.0.8", "hashPath": "system.text.encodings.web.9.0.8.nupkg.sha512"}, "System.Text.Json/9.0.8": {"type": "package", "serviceable": true, "sha512": "sha512-mIQir9jBqk0V7X0Nw5hzPJZC8DuGdf+2DS3jAVsr6rq5+/VyH5rza0XGcONJUWBrZ+G6BCwNyjWYd9lncBu48A==", "path": "system.text.json/9.0.8", "hashPath": "system.text.json.9.0.8.nupkg.sha512"}}}